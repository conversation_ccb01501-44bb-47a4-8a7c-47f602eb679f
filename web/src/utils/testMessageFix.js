/**
 * 测试消息重复修复效果
 */

import webSocketManager from '@/utils/websocket.js'
import { getChatMessages } from '@/utils/db.js'

// 模拟WebSocket消息
const createTestMessage = (id, type = 1) => ({
  id: `test_${id}`,
  typecode: type, // 1=私聊, 2=群聊
  typecode2: 0,
  fromid: '12345',
  toid: '67890',
  groupID: type === 2 ? '999' : undefined,
  t: new Date().toISOString(),
  msg: `测试消息${id}`,
  senderNickname: '测试用户',
  senderAvatar: ''
})

// 测试消息处理器数量
export const testMessageHandlers = () => {
  console.log('=== 测试消息处理器数量 ===')
  
  const handlers = webSocketManager.messageHandlers
  console.log('当前注册的消息处理器:')
  
  handlers.forEach((handler, type) => {
    console.log(`- ${type}: ${handler.name || '匿名函数'}`)
  })
  
  console.log(`总计: ${handlers.size} 个处理器`)
  
  return {
    totalHandlers: handlers.size,
    handlerTypes: Array.from(handlers.keys())
  }
}

// 测试消息处理流程
export const testMessageProcessing = async () => {
  console.log('=== 测试消息处理流程 ===')
  
  // 记录处理次数
  let processCount = 0
  const processLog = []
  
  // 临时拦截console.log来监控处理过程
  const originalLog = console.log
  console.log = (...args) => {
    const message = args.join(' ')
    if (message.includes('收到WebSocket消息:') || 
        message.includes('处理私聊消息:') || 
        message.includes('处理群聊消息:')) {
      processCount++
      processLog.push({
        timestamp: Date.now(),
        message: message,
        count: processCount
      })
    }
    originalLog.apply(console, args)
  }
  
  try {
    // 发送测试消息
    const testMsg = createTestMessage(Date.now())
    console.log('发送测试消息:', testMsg)
    
    // 模拟WebSocket消息接收
    webSocketManager.handleMessage(testMsg)
    
    // 等待处理完成
    await new Promise(resolve => setTimeout(resolve, 1000))
    
    // 恢复console.log
    console.log = originalLog
    
    console.log('处理日志:', processLog)
    console.log('总处理次数:', processCount)
    
    return {
      success: processCount <= 2, // 允许最多2次处理（Pinia状态更新 + ConnectionService处理）
      processCount,
      processLog,
      message: processCount > 2 ? '检测到重复处理' : '处理正常'
    }
    
  } catch (error) {
    console.log = originalLog
    console.error('测试过程中发生错误:', error)
    return {
      success: false,
      error: error.message
    }
  }
}

// 测试数据库存储
export const testDatabaseStorage = async () => {
  console.log('=== 测试数据库存储 ===')
  
  const testChatId = `test_chat_${Date.now()}`
  const testMsg = createTestMessage(Date.now())
  testMsg.chatid = testChatId
  
  try {
    // 发送消息
    webSocketManager.handleMessage(testMsg)
    
    // 等待处理完成
    await new Promise(resolve => setTimeout(resolve, 2000))
    
    // 检查数据库中的消息
    const messages = await getChatMessages(testChatId, 'private', 1, 10)
    
    console.log(`数据库中的消息数量: ${messages.length}`)
    console.log('消息详情:', messages)
    
    return {
      success: messages.length === 1, // 应该只有一条消息
      messageCount: messages.length,
      messages,
      message: messages.length === 1 ? '存储正常' : `存储异常，发现${messages.length}条消息`
    }
    
  } catch (error) {
    console.error('数据库测试失败:', error)
    return {
      success: false,
      error: error.message
    }
  }
}

// 综合测试
export const runComprehensiveTest = async () => {
  console.log('🧪 开始综合测试...')
  
  const results = {
    timestamp: new Date().toISOString(),
    tests: {}
  }
  
  try {
    // 测试1: 消息处理器数量
    console.log('\n1. 测试消息处理器数量...')
    results.tests.handlers = testMessageHandlers()
    
    // 测试2: 消息处理流程
    console.log('\n2. 测试消息处理流程...')
    results.tests.processing = await testMessageProcessing()
    
    // 测试3: 数据库存储
    console.log('\n3. 测试数据库存储...')
    results.tests.storage = await testDatabaseStorage()
    
    // 综合评估
    const allPassed = Object.values(results.tests).every(test => test.success)
    results.overall = {
      success: allPassed,
      message: allPassed ? '所有测试通过' : '部分测试失败',
      issues: Object.entries(results.tests)
        .filter(([, test]) => !test.success)
        .map(([name, test]) => `${name}: ${test.message || test.error}`)
    }
    
    console.log('\n🧪 综合测试完成:', results.overall)
    return results
    
  } catch (error) {
    console.error('综合测试失败:', error)
    results.overall = {
      success: false,
      message: '测试执行失败',
      error: error.message
    }
    return results
  }
}

// 导出到全局对象供调试使用
if (typeof window !== 'undefined') {
  window.testMessageFix = {
    testHandlers: testMessageHandlers,
    testProcessing: testMessageProcessing,
    testStorage: testDatabaseStorage,
    runAll: runComprehensiveTest
  }
  
  console.log('🔧 消息修复测试工具已加载')
  console.log('使用 window.testMessageFix.runAll() 运行完整测试')
}
