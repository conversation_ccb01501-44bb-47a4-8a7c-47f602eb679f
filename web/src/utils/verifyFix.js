/**
 * 验证WebSocket消息重复修复
 */

import { useWebSocketStore } from '@/pinia/modules/websocket.js'

// 验证WebSocket store中的必要函数是否存在
export const verifyWebSocketStoreFunctions = () => {
  console.log('=== 验证WebSocket Store函数 ===')
  
  const webSocketStore = useWebSocketStore()
  
  const requiredFunctions = [
    'clearUnreadCount',
    'clearMessageHistory', 
    'reconnect',
    'sendMessage',
    'closeConnection',
    'forceReconnect',
    'initConnection',
    'loadChatList',
    'loadChatMessages',
    'markChatAsRead'
  ]
  
  const results = {}
  
  requiredFunctions.forEach(funcName => {
    const exists = typeof webSocketStore[funcName] === 'function'
    results[funcName] = exists
    console.log(`${funcName}: ${exists ? '✅ 存在' : '❌ 缺失'}`)
  })
  
  const allExists = Object.values(results).every(exists => exists)
  
  console.log(`\n总结: ${allExists ? '✅ 所有必要函数都存在' : '❌ 部分函数缺失'}`)
  
  return {
    success: allExists,
    results,
    missing: Object.keys(results).filter(key => !results[key])
  }
}

// 测试clearUnreadCount函数
export const testClearUnreadCount = () => {
  console.log('=== 测试clearUnreadCount函数 ===')
  
  try {
    const webSocketStore = useWebSocketStore()
    
    // 设置一个未读数量
    webSocketStore.unreadCount = 5
    console.log('设置未读数量为:', webSocketStore.unreadCount)
    
    // 调用清除函数
    webSocketStore.clearUnreadCount()
    console.log('调用clearUnreadCount后，未读数量为:', webSocketStore.unreadCount)
    
    const success = webSocketStore.unreadCount === 0
    console.log(`测试结果: ${success ? '✅ 成功' : '❌ 失败'}`)
    
    return {
      success,
      message: success ? '清除未读数量功能正常' : '清除未读数量功能异常'
    }
    
  } catch (error) {
    console.error('测试clearUnreadCount失败:', error)
    return {
      success: false,
      message: '测试过程中发生错误: ' + error.message
    }
  }
}

// 测试消息处理器数量
export const testMessageHandlerCount = () => {
  console.log('=== 测试消息处理器数量 ===')
  
  try {
    const webSocketManager = require('@/utils/websocket.js').default
    const handlerCount = webSocketManager.messageHandlers.size
    
    console.log('当前消息处理器数量:', handlerCount)
    console.log('处理器类型:', Array.from(webSocketManager.messageHandlers.keys()))
    
    // 期望的处理器：'all' (Pinia状态更新) + 'connectionService' (统一消息处理)
    const expectedHandlers = ['all', 'connectionService']
    const actualHandlers = Array.from(webSocketManager.messageHandlers.keys())
    
    const hasExpectedHandlers = expectedHandlers.every(handler => 
      actualHandlers.includes(handler)
    )
    
    console.log(`期望的处理器: ${expectedHandlers.join(', ')}`)
    console.log(`实际的处理器: ${actualHandlers.join(', ')}`)
    console.log(`处理器检查: ${hasExpectedHandlers ? '✅ 正常' : '❌ 异常'}`)
    
    return {
      success: hasExpectedHandlers && handlerCount >= 2,
      handlerCount,
      actualHandlers,
      expectedHandlers,
      message: hasExpectedHandlers ? '消息处理器配置正常' : '消息处理器配置异常'
    }
    
  } catch (error) {
    console.error('测试消息处理器失败:', error)
    return {
      success: false,
      message: '测试过程中发生错误: ' + error.message
    }
  }
}

// 综合验证
export const runVerification = () => {
  console.log('🔍 开始验证WebSocket消息重复修复...\n')
  
  const results = {
    timestamp: new Date().toISOString(),
    tests: {}
  }
  
  // 测试1: 验证函数存在性
  console.log('1. 验证WebSocket Store函数...')
  results.tests.functions = verifyWebSocketStoreFunctions()
  
  // 测试2: 测试clearUnreadCount功能
  console.log('\n2. 测试clearUnreadCount功能...')
  results.tests.clearUnread = testClearUnreadCount()
  
  // 测试3: 测试消息处理器配置
  console.log('\n3. 测试消息处理器配置...')
  results.tests.handlers = testMessageHandlerCount()
  
  // 综合评估
  const allPassed = Object.values(results.tests).every(test => test.success)
  results.overall = {
    success: allPassed,
    message: allPassed ? '✅ 所有验证通过，修复成功' : '❌ 部分验证失败',
    issues: Object.entries(results.tests)
      .filter(([, test]) => !test.success)
      .map(([name, test]) => `${name}: ${test.message}`)
  }
  
  console.log('\n🔍 验证完成:', results.overall.message)
  if (results.overall.issues.length > 0) {
    console.log('问题列表:', results.overall.issues)
  }
  
  return results
}

// 导出到全局对象供调试使用
if (typeof window !== 'undefined') {
  window.verifyFix = {
    verifyFunctions: verifyWebSocketStoreFunctions,
    testClearUnread: testClearUnreadCount,
    testHandlers: testMessageHandlerCount,
    runAll: runVerification
  }
  
  console.log('🔧 修复验证工具已加载')
  console.log('使用 window.verifyFix.runAll() 运行完整验证')
}
