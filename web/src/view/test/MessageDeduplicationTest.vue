<template>
  <div class="message-dedup-test">
    <el-card header="WebSocket消息去重测试">
      <div class="test-section">
        <h3>测试状态</h3>
        <el-tag :type="testStatus.type">{{ testStatus.text }}</el-tag>
        
        <div class="test-controls">
          <el-button @click="runQuickTest" :loading="testing" type="primary">
            运行快速测试
          </el-button>
          <el-button @click="startMonitoring" :disabled="monitoring" type="success">
            开始监控
          </el-button>
          <el-button @click="stopMonitoring" :disabled="!monitoring" type="warning">
            停止监控
          </el-button>
          <el-button @click="clearResults" type="info">
            清除结果
          </el-button>
        </div>
      </div>

      <div class="test-results" v-if="testResults">
        <h3>测试结果</h3>
        <el-alert 
          :title="testResults.summary.success ? '测试通过' : '测试失败'" 
          :type="testResults.summary.success ? 'success' : 'error'"
          :description="testResults.summary.issues.join(', ') || '未发现问题'"
          show-icon
        />
        
        <el-collapse v-model="activeCollapse" class="result-details">
          <el-collapse-item title="监控结果" name="monitoring">
            <div>
              <p><strong>总消息数:</strong> {{ testResults.monitoring.totalMessages }}</p>
              <p><strong>重复警告:</strong> {{ testResults.monitoring.duplicateWarnings.length }}</p>
              
              <el-table :data="testResults.monitoring.log.slice(-10)" size="small">
                <el-table-column prop="timestamp" label="时间" width="180" />
                <el-table-column prop="source" label="来源" width="120" />
                <el-table-column prop="message" label="消息" />
              </el-table>
            </div>
          </el-collapse-item>
          
          <el-collapse-item title="去重测试" name="deduplication">
            <div>
              <p><strong>尝试插入:</strong> {{ testResults.deduplication.details?.attemptedInserts || 0 }}</p>
              <p><strong>实际存储:</strong> {{ testResults.deduplication.details?.actualMessages || 0 }}</p>
              
              <el-tag 
                v-for="(result, index) in testResults.deduplication.details?.insertResults || []" 
                :key="index"
                :type="result.includes('成功') ? 'success' : 'danger'"
                class="result-tag"
              >
                {{ result }}
              </el-tag>
            </div>
          </el-collapse-item>
        </el-collapse>
      </div>

      <div class="live-monitoring" v-if="monitoring">
        <h3>实时监控</h3>
        <div class="monitor-stats">
          <el-statistic title="消息计数" :value="monitorStats.messageCount" />
          <el-statistic title="重复警告" :value="monitorStats.duplicateCount" />
        </div>
        
        <div class="recent-messages">
          <h4>最近消息</h4>
          <div 
            v-for="(msg, index) in recentMessages.slice(-5)" 
            :key="index"
            class="message-item"
          >
            <span class="timestamp">{{ formatTime(msg.timestamp) }}</span>
            <span class="source">{{ msg.source }}</span>
            <span class="content">{{ msg.message.substring(0, 100) }}...</span>
          </div>
        </div>
      </div>
    </el-card>
  </div>
</template>

<script setup>
import { ref, reactive, onMounted, onUnmounted } from 'vue'
import { ElMessage } from 'element-plus'
import { quickTest, monitorWebSocketMessages } from '@/utils/messageDeduplicationTest.js'

defineOptions({
  name: 'MessageDeduplicationTest'
})

// 响应式数据
const testing = ref(false)
const monitoring = ref(false)
const testResults = ref(null)
const activeCollapse = ref(['monitoring', 'deduplication'])

const testStatus = reactive({
  type: 'info',
  text: '准备就绪'
})

const monitorStats = reactive({
  messageCount: 0,
  duplicateCount: 0
})

const recentMessages = ref([])
let monitor = null

// 运行快速测试
const runQuickTest = async () => {
  testing.value = true
  testStatus.type = 'warning'
  testStatus.text = '测试中...'
  
  try {
    const result = await quickTest()
    testResults.value = result
    
    if (result.summary.success) {
      testStatus.type = 'success'
      testStatus.text = '测试通过'
      ElMessage.success('消息去重测试通过！')
    } else {
      testStatus.type = 'danger'
      testStatus.text = '测试失败'
      ElMessage.error('检测到消息重复处理问题')
    }
  } catch (error) {
    testStatus.type = 'danger'
    testStatus.text = '测试错误'
    ElMessage.error('测试过程中发生错误: ' + error.message)
  } finally {
    testing.value = false
  }
}

// 开始监控
const startMonitoring = () => {
  monitor = monitorWebSocketMessages()
  monitoring.value = true
  testStatus.type = 'warning'
  testStatus.text = '监控中...'
  
  // 定期更新监控统计
  const updateStats = () => {
    if (monitoring.value && monitor) {
      const log = monitor.getLog()
      monitorStats.messageCount = monitor.getCount()
      monitorStats.duplicateCount = log.filter(entry => entry.message.includes('⚠️')).length
      recentMessages.value = log
      
      setTimeout(updateStats, 1000)
    }
  }
  updateStats()
  
  ElMessage.info('开始监控WebSocket消息处理')
}

// 停止监控
const stopMonitoring = () => {
  if (monitor) {
    const result = monitor.stop()
    monitoring.value = false
    testStatus.type = 'info'
    testStatus.text = '监控已停止'
    
    ElMessage.success(`监控已停止，共处理 ${result.totalMessages} 条消息`)
  }
}

// 清除结果
const clearResults = () => {
  testResults.value = null
  recentMessages.value = []
  monitorStats.messageCount = 0
  monitorStats.duplicateCount = 0
  testStatus.type = 'info'
  testStatus.text = '准备就绪'
}

// 格式化时间
const formatTime = (timestamp) => {
  return new Date(timestamp).toLocaleTimeString()
}

// 组件卸载时清理
onUnmounted(() => {
  if (monitoring.value && monitor) {
    monitor.stop()
  }
})
</script>

<style scoped>
.message-dedup-test {
  padding: 20px;
}

.test-section {
  margin-bottom: 20px;
}

.test-controls {
  margin-top: 15px;
}

.test-controls .el-button {
  margin-right: 10px;
}

.test-results {
  margin-top: 20px;
}

.result-details {
  margin-top: 15px;
}

.result-tag {
  margin-right: 8px;
  margin-bottom: 5px;
}

.live-monitoring {
  margin-top: 20px;
  padding-top: 20px;
  border-top: 1px solid #eee;
}

.monitor-stats {
  display: flex;
  gap: 40px;
  margin-bottom: 20px;
}

.recent-messages {
  max-height: 300px;
  overflow-y: auto;
}

.message-item {
  display: flex;
  gap: 10px;
  padding: 5px 0;
  border-bottom: 1px solid #f0f0f0;
  font-size: 12px;
}

.timestamp {
  color: #666;
  min-width: 80px;
}

.source {
  color: #409eff;
  min-width: 100px;
  font-weight: bold;
}

.content {
  color: #333;
  flex: 1;
}
</style>
