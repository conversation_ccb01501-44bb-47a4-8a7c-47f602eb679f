<template>
  <el-dialog
    v-model="visible"
    title=""
    :width="1200"
    :height="750"
    :show-close="false"
    :close-on-click-modal="false"
    :close-on-press-escape="false"
    class="chat-dialog"
    destroy-on-close
  >
    <template #header>
      <div class="dialog-header flex justify-between items-center">
        <div class="header-left">
        </div>
        <div class="header-right">
          <el-button @click="closeDialog" type="danger" size="small" circle>
            <el-icon><Close /></el-icon>
          </el-button>
        </div>
      </div>
    </template>

    <div class="chat-container flex h-full bg-gray-800">
      <!-- 左侧会话列表 -->
      <div class="conversation-sidebar w-[350px] bg-gray-900 border-r border-gray-700 flex flex-row">
        <div class="sidebar-tabs w-16 flex flex-col bg-gray-950 flex-shrink-0">
          <div
            class="tab-item flex flex-col items-center justify-center gap-1 py-4 px-2 text-gray-400 cursor-pointer transition-all duration-200 hover:bg-gray-800 hover:text-white"
            :class="{ 'bg-green-600 text-white': activeTab === 'groups' }"
            @click="activeTab = 'groups'"
          >
            <el-icon class="text-lg"><ChatLineRound /></el-icon>
            <span class="text-xs font-medium text-center">群聊</span>
          </div>
          <div
            class="tab-item flex flex-col items-center justify-center gap-1 py-4 px-2 text-gray-400 cursor-pointer transition-all duration-200 hover:bg-gray-800 hover:text-white"
            :class="{ 'bg-green-600 text-white': activeTab === 'friends' }"
            @click="activeTab = 'friends'"
          >
            <el-icon class="text-lg"><User /></el-icon>
            <span class="text-xs font-medium text-center">私聊</span>
          </div>
        </div>

        <div class="conversation-list flex-1 overflow-y-auto bg-gray-900">
          <!-- 群组会话 -->
          <div v-if="activeTab === 'groups'" class="conversation-section p-2 h-full">
            <div v-if="groupConversations.length === 0" class="empty-state text-center text-gray-400 py-8">
              <p>正在加载群组列表...</p>
              <p class="text-xs mt-2">群组数量: {{ groupConversations.length }}</p>
              <p class="text-xs mt-1">当前标签页: {{ activeTab }}</p>
              <p class="text-xs mt-1">数据状态: {{ JSON.stringify(groupConversations) }}</p>
            </div>

            <!-- <div v-else class="text-xs text-gray-400 mb-2">
              找到 {{ groupConversations.length }} 个群组
            </div> -->

            <ConversationCard
              v-for="group in groupConversations"
              :key="group.id"
              :conversation="group"
              :is-selected="selectedConversation?.id === group.id"
              @click="selectConversation(group)"
            />
          </div>

          <!-- 好友会话 -->
          <div v-if="activeTab === 'friends'" class="conversation-section p-2 h-full">
            <div v-if="friendConversations.length === 0" class="empty-state text-center text-gray-400 py-8">
              <p>暂无好友会话</p>
              <p class="text-xs mt-2">在群聊中点击成员头像开始私聊</p>
            </div>

            <ConversationCard
              v-for="friend in friendConversations"
              :key="friend.id"
              :conversation="friend"
              :is-selected="selectedConversation?.id === friend.id"
              @click="selectConversation(friend)"
            />
          </div>
        </div>
      </div>

      <!-- 右侧聊天区域 -->
      <div class="chat-main flex-1 flex flex-col bg-gray-700">
        <MessagePanel
          v-if="selectedConversation"
          :conversation="selectedConversation"
          @start-chat="handleStartPrivateChat"
        />
        <div v-else class="empty-chat flex items-center justify-center h-full rounded-lg">
          <el-empty description="请选择一个会话开始聊天" />
        </div>
      </div>

      <!-- 群组成员面板 -->
     <!--  <div v-if="selectedConversation?.type === 'group'" class="group-members bg-gray-800 border-l border-gray-600">
        <GroupUserPanel
          :group="selectedConversation?.originalData"
          @start-chat="handleStartPrivateChat"
        />
      </div> -->
    </div>
  </el-dialog>
</template>

<script setup>
import { ref, computed, watch, onMounted, onUnmounted } from 'vue'
import { ElMessage } from 'element-plus'
import ConversationCard from './components/ConversationCard.vue'
import MessagePanel from './components/MessagePanel.vue'
import GroupUserPanel from './components/GroupUserPanel.vue'
import { getGroupList } from '@/api/im/group.js'
import { useWebSocketStore } from '@/pinia/modules/websocket.js'
import { useUserStore } from '@/pinia/modules/user.js'
import { emitter } from '@/utils/bus.js'

defineOptions({
  name: 'ChatDialog'
})

// Props
const props = defineProps({
  modelValue: {
    type: Boolean,
    default: false
  },
  user: {
    type: Object,
    default: null
  }
})

// Emits
const emit = defineEmits(['update:modelValue', 'close'])

// 响应式数据
const visible = computed({
  get: () => props.modelValue,
  set: (value) => emit('update:modelValue', value)
})

const currentUser = ref(props.user)
const activeTab = ref('groups')
const selectedConversation = ref(null)
const webSocketStore = useWebSocketStore()
const userStore = useUserStore()

// 群组会话数据
const groupConversations = ref([])

const friendConversations = ref([])


// 方法
const selectConversation = (conversation) => {
  selectedConversation.value = conversation
}

const closeDialog = () => {
  visible.value = false
  emit('close')
}

// 处理开始私聊事件
const handleStartPrivateChat = async (privateChatData) => {
  console.log('处理私聊事件:', privateChatData)

  try {
    // 检查是否已存在该好友会话
    const targetUserId = privateChatData.originalData?.id || privateChatData.originalData?.ID || privateChatData.originalData?.userId
    const existingFriend = friendConversations.value.find(
      friend => (friend.originalData?.id || friend.originalData?.ID) === targetUserId
    )

    if (existingFriend) {
      // 如果已存在，直接切换到该会话
      console.log('好友会话已存在，直接切换:', existingFriend)
      activeTab.value = 'friends'
      selectedConversation.value = existingFriend
    } else {
      // 创建新的好友会话记录
      console.log('创建新的好友会话记录')

      // 添加到好友会话列表
      friendConversations.value.unshift(privateChatData)

      // 使用新的聊天列表管理器保存好友会话
      await saveFriendConversationToDB(privateChatData)

      // 切换到好友标签页并选择该会话
      activeTab.value = 'friends'
      selectedConversation.value = privateChatData

      console.log('好友会话创建成功:', privateChatData)
    }
  } catch (error) {
    console.error('处理私聊事件失败:', error)
    ElMessage.error('创建私聊会话失败')
  }
}

// 保存好友会话信息到数据库
const saveFriendConversationToDB = async (friendData) => {
  try {
    const { addChatListItem } = await import('@/utils/chatListManager.js')

    const currentUserId = getCurrentUserId()
    if (!currentUserId) {
      console.error('无法获取当前用户ID，无法保存好友会话')
      return
    }

    // 获取目标用户ID
    const targetUserId = friendData.originalData?.id || friendData.originalData?.ID || friendData.originalData?.userId || friendData.userId

    // 创建聊天列表项 - 使用新的存储格式
    const chatListItem = {
      chatId: targetUserId.toString(), // 直接使用对方用户ID作为chatId
      chatType: 'private',
      type: 'private', // 添加type字段以保持一致性
      name: friendData.name,
      avatar: friendData.avatar || '',
      lastMessage: '开始聊天',
      lastTime: new Date().toISOString(),
      unreadCount: 0,
      participants: [currentUserId, targetUserId],
      targetUserId: targetUserId, // 明确存储目标用户ID
      createdAt: new Date().toISOString(),
      updatedAt: new Date().toISOString(),
      isPersistent: true
    }

    await addChatListItem(chatListItem)
    console.log('好友会话已保存到聊天列表:', chatListItem)
  } catch (error) {
    console.error('保存好友会话到数据库失败:', error)
  }
}

// 获取当前用户ID
const getCurrentUserId = () => {
  // 首先尝试从用户store获取
  if (userStore.userInfo && (userStore.userInfo.ID || userStore.userInfo.uuid || userStore.userInfo.id)) {
    return userStore.userInfo.ID || userStore.userInfo.uuid || userStore.userInfo.id
  }

  // 然后从localStorage/sessionStorage获取
  const storedUserId = localStorage.getItem('userId') || sessionStorage.getItem('userId')
  if (storedUserId) {
    return storedUserId
  }

  // 如果都没有，返回null或抛出错误
  console.warn('无法获取当前用户ID，用户可能未登录')
  return null
}

// 获取群聊的最后一条消息
const getGroupLastMessage = async (groupId) => {
  try {
    const { getChatMessages } = await import('@/utils/db.js')
    const messages = await getChatMessages(groupId.toString(), 1, 1)

    if (messages && messages.length > 0) {
      const lastMessage = messages[0]
      return {
        content: lastMessage.msg || lastMessage.lastMessage || '',
        time: lastMessage.t || lastMessage.timestamp,
        type: lastMessage.typecode2 || 0
      }
    }
    return null
  } catch (error) {
    console.warn('获取群聊最后一条消息失败:', error)
    return null
  }
}

// 获取群聊的未读消息数量
const getGroupUnreadCount = async (groupId) => {
  try {
    // 从Pinia store或本地存储获取未读数量
    const chatList = webSocketStore.chatList || {}
    const chatInfo = chatList[groupId.toString()]

    if (chatInfo && chatInfo.unreadCount) {
      return chatInfo.unreadCount
    }

    // 如果store中没有，尝试从数据库计算未读数量
    // 这里可以根据实际需求实现更复杂的未读计算逻辑
    return 0
  } catch (error) {
    console.warn('获取群聊未读数量失败:', error)
    return 0
  }
}

// 格式化消息内容显示
const formatMessageContent = (content, type) => {
  if (!content) return '暂无消息'

  // 根据消息类型格式化显示内容
  switch (type) {
    case 1: // 音频
      return '[语音]'
    case 2: // 图片
      return '[图片]'
    case 3: // 视频
      return '[语音]'
    case 4: // 转发消息
      return '[转发消息]'
    case 5: // 撤回
      return '[消息已撤回]'
    case 9: // 语音通话
      return '[语音通话]'
    case 10: // 视频通话
      return '[视频通话]'
    default: // 文本消息
      // 限制显示长度
      return content.length > 30 ? content.substring(0, 30) + '...' : content
  }
}

// 获取群组列表
const fetchGroupList = async () => {
  try {
    console.log('开始获取群组列表...')
    const params = {
      page: 1,
      pageSize: 100
    }
    const response = await getGroupList(params)

    console.log('API 获取群组列表:', response)

    // 检查响应数据结构
    const responseData = response.data || response
    console.log('响应数据:', responseData)
    console.log('响应数据类型:', typeof responseData)
    console.log('响应数据code:', responseData.code)
    console.log('响应数据data:', responseData.data)

    if (responseData.code == 0 && responseData.data && responseData.data.list) {
      console.log('群组数据:', responseData.data)
      console.log('群组列表:', responseData.data.list)
      console.log('群组列表长度:', responseData.data.list.length)

      // 先直接转换群组数据，不获取最后消息（简化处理）
      const groupsData = responseData.data.list.map((group) => {
        console.log('处理群组:', group)
        return {
          id: `group_${group.id}`,
          type: 'group',
          name: group.groupName,
          avatar: group.groupHeader || '',
          lastMessage: '暂无消息',
          lastTime: formatTime(group.createdAt),
          unread: 0,
          online: true,
          // 保存原始群组数据，用于后续聊天功能
          originalData: group
        }
      })

      groupConversations.value = groupsData
      console.log('转换后的群组数据:', groupConversations.value)
      console.log('groupConversations.value.length:', groupConversations.value.length)

      // 如果当前是群聊标签页且有群组数据，自动选择第一个群组
      if (activeTab.value === 'groups' && groupConversations.value.length > 0) {
        selectedConversation.value = groupConversations.value[0]
        console.log('自动选择第一个群组:', selectedConversation.value)
      }
    } else {
      console.log('API 响应格式不正确或无数据:', responseData)
      console.log('尝试其他可能的数据结构...')

      // 尝试其他可能的数据结构
      let groupList = null
      if (responseData.list) {
        groupList = responseData.list
      } else if (responseData.data && Array.isArray(responseData.data)) {
        groupList = responseData.data
      } else if (Array.isArray(responseData)) {
        groupList = responseData
      }

      if (groupList && groupList.length > 0) {
        console.log('找到群组列表:', groupList)
        const groupsData = groupList.map((group) => {
          console.log('处理群组:', group)
          return {
            id: `group_${group.id}`,
            type: 'group',
            name: group.groupName || group.name || `群组${group.id}`,
            avatar: group.groupHeader || group.avatar || '',
            lastMessage: '暂无消息',
            lastTime: formatTime(group.createdAt || new Date()),
            unread: 0,
            online: true,
            originalData: group
          }
        })

        groupConversations.value = groupsData
        console.log('备用方案转换后的群组数据:', groupConversations.value)
      }
    }
  } catch (error) {
    console.error('获取群组列表失败:', error)
    console.error('错误详情:', error.message)
    console.error('错误堆栈:', error.stack)
  }
}

// 格式化时间
const formatTime = (timeStr) => {
  const date = new Date(timeStr)
  const now = new Date()
  const today = new Date(now.getFullYear(), now.getMonth(), now.getDate())
  const messageDate = new Date(date.getFullYear(), date.getMonth(), date.getDate())

  if (messageDate.getTime() === today.getTime()) {
    // 今天的消息显示时间
    return date.toLocaleTimeString('zh-CN', { hour: '2-digit', minute: '2-digit' })
  } else if (messageDate.getTime() === today.getTime() - 24 * 60 * 60 * 1000) {
    // 昨天的消息
    return '昨天'
  } else {
    // 更早的消息显示日期
    return date.toLocaleDateString('zh-CN', { month: '2-digit', day: '2-digit' })
  }
}

// 初始化WebSocket连接
const initWebSocket = async () => {
  try {
    // 从用户状态获取token
    const token = userStore.token

    if (!token) {
      console.warn('用户未登录或token不存在，无法初始化WebSocket连接')
      return
    }

    if (!webSocketStore.isConnected) {
      console.log('初始化WebSocket连接...')
      await webSocketStore.initConnection(token, true)
      console.log('WebSocket连接已建立')
    } else {
      console.log('WebSocket已连接')
    }
  } catch (error) {
    console.error('WebSocket连接失败:', error)
  }
}

// 从IndexedDB加载聊天列表
const loadChatListFromDB = async () => {
  try {
    console.log('从IndexedDB加载聊天列表...')
    const { getChatList } = await import('@/utils/chatListManager.js')
    
    // 分别加载群聊和私聊列表
    const groupChatList = await getChatList('group')
    const privateChatList = await getChatList('private')

    console.log('从聊天列表管理器加载的群聊数据:', groupChatList)
    console.log('从聊天列表管理器加载的私聊数据:', privateChatList)
    console.log('当前用户ID:', getCurrentUserId())

    // 处理群聊列表 - 重新构建整个列表以确保数据同步
    const newGroupConversations = []
    if (groupChatList && groupChatList.length > 0) {
      groupChatList.forEach(chatItem => {
        const actualId = chatItem.chatId
        if (!actualId) return

        const conversation = {
          id: `group_${actualId}`,
          type: 'group',
          name: chatItem.name || `群聊${actualId}`,
          avatar: chatItem.avatar || '',
          lastMessage: chatItem.lastMessage || '暂无消息',
          lastTime: formatTime(chatItem.lastTime || chatItem.updatedAt),
          unread: chatItem.unreadCount || 0,
          online: true,
          originalData: {
            id: parseInt(actualId),
            ID: parseInt(actualId),
            groupName: chatItem.name,
            groupHeader: chatItem.avatar || ''
          }
        }

        newGroupConversations.push(conversation)
      })
    }

    // 合并API数据中的群聊（如果不在数据库中）
    groupConversations.value.forEach(existingGroup => {
      const groupId = existingGroup.originalData?.id || existingGroup.originalData?.ID
      const existsInDB = newGroupConversations.some(dbGroup =>
        (dbGroup.originalData?.id || dbGroup.originalData?.ID) === groupId
      )

      if (!existsInDB) {
        newGroupConversations.push(existingGroup)
      }
    })

    // 按时间排序
    newGroupConversations.sort((a, b) => {
      const timeA = new Date(a.lastTime || 0).getTime()
      const timeB = new Date(b.lastTime || 0).getTime()
      return timeB - timeA
    })

    groupConversations.value = newGroupConversations
    console.log('更新后的群组会话:', groupConversations.value)

    // 处理私聊列表 - 重新构建整个列表以确保数据同步
    const newFriendConversations = []
    if (privateChatList && privateChatList.length > 0) {
      for (const chatItem of privateChatList) {
        // 私聊数据处理：需要从participants或targetUserId中获取对方用户ID
        let targetUserId

        if (chatItem.targetUserId) {
          // 如果有targetUserId字段，直接使用
          targetUserId = chatItem.targetUserId
        } else if (chatItem.participants && chatItem.participants.length === 2) {
          // 从participants中获取对方用户ID
          const currentUserId = getCurrentUserId()
          targetUserId = chatItem.participants.find(id => String(id) !== String(currentUserId))
        } else if (chatItem.chatType === 'private' && chatItem.chatId) {
          // 对于新的存储格式，chatId可能直接是对方用户ID
          targetUserId = chatItem.chatId
        }

        if (!targetUserId) {
          console.warn('无法确定私聊对方用户ID:', chatItem)
          continue
        }

        // 如果数据库中的用户信息不完整，尝试获取最新的用户信息
        let userName = chatItem.name
        let userAvatar = chatItem.avatar || ''

        if (!userName || userName.startsWith('用户')) {
          try {
            const { getUserInfo } = await import('@/utils/avatarService.js')
            const userInfo = await getUserInfo(targetUserId)
            if (userInfo && userInfo.nickname && !userInfo.nickname.startsWith('用户')) {
              userName = userInfo.nickname
              userAvatar = userInfo.avatar || userAvatar

              // 更新数据库中的用户信息
              try {
                const { updateChatListItem } = await import('@/utils/chatListManager.js')
                await updateChatListItem(chatItem.chatId, {
                  name: userName,
                  avatar: userAvatar
                })
                console.log('已更新数据库中的用户信息:', targetUserId, userName)
              } catch (updateError) {
                console.warn('更新数据库用户信息失败:', updateError)
              }
            }
          } catch (error) {
            console.warn('获取用户信息失败:', error)
          }
        }

        const conversation = {
          id: `friend_${targetUserId}`,
          type: 'friend',
          name: userName || `用户${targetUserId}`,
          avatar: userAvatar,
          lastMessage: chatItem.lastMessage || '暂无消息',
          lastTime: formatTime(chatItem.lastTime || chatItem.updatedAt),
          unread: chatItem.unreadCount || 0,
          online: true,
          originalData: {
            id: parseInt(targetUserId),
            ID: parseInt(targetUserId),
            nickname: userName,
            avatar: userAvatar,
            phone: ''
          }
        }

        newFriendConversations.push(conversation)
      }
    }

    // 合并现有的私聊数据（如果不在数据库中）
    friendConversations.value.forEach(existingFriend => {
      const friendId = existingFriend.originalData?.id || existingFriend.originalData?.ID
      const existsInDB = newFriendConversations.some(dbFriend =>
        (dbFriend.originalData?.id || dbFriend.originalData?.ID) === friendId
      )

      if (!existsInDB) {
        newFriendConversations.push(existingFriend)
      }
    })

    // 按时间排序
    newFriendConversations.sort((a, b) => {
      const timeA = new Date(a.lastTime || 0).getTime()
      const timeB = new Date(b.lastTime || 0).getTime()
      return timeB - timeA
    })

    friendConversations.value = newFriendConversations
    console.log('更新后的好友会话:', friendConversations.value)

    // 根据当前标签页自动选择会话
    if (activeTab.value === 'groups' && groupConversations.value.length > 0 && !selectedConversation.value) {
      selectedConversation.value = groupConversations.value[0]
      console.log('自动选择第一个群组:', selectedConversation.value)
    } else if (activeTab.value === 'friends' && friendConversations.value.length > 0 && !selectedConversation.value) {
      selectedConversation.value = friendConversations.value[0]
      console.log('自动选择第一个私聊:', selectedConversation.value)
    }
  } catch (error) {
    console.error('从数据库加载聊天列表失败:', error)
  }
}

// 组件挂载时获取群组列表和初始化WebSocket
onMounted(async () => {
  console.log('组件已挂载，开始初始化...')

  // 确保获取了聊天用户信息
  if (!userStore.formChatId) {
    console.log('获取聊天用户信息...')
    await userStore.GetChatUserInfo()
  }


  await initWebSocket()
  await loadChatListFromDB() // 先加载数据库中的聊天列表
  await fetchGroupList() // 再获取API数据
})

// 监听用户变化
watch(() => props.user, (newUser) => {
  currentUser.value = newUser
}, { immediate: true })

// 更新群聊列表中的最后一条消息
const updateGroupLastMessage = async (groupId, messageData) => {
  console.log('开始更新群聊最后消息:', { groupId, messageData })

  // 确保groupId是字符串或数字类型
  const targetGroupId = String(groupId)

  const groupIndex = groupConversations.value.findIndex(
    group => String(group.originalData?.id || group.originalData?.ID) === targetGroupId
  )

  console.log('查找群聊索引:', { targetGroupId, groupIndex, totalGroups: groupConversations.value.length })

  if (groupIndex !== -1) {
    const group = groupConversations.value[groupIndex]
    const oldLastMessage = group.lastMessage
    const oldLastTime = group.lastTime

    // 更新最后消息内容和时间
    group.lastMessage = formatMessageContent(
      messageData.msg || messageData.content,
      messageData.typecode2 || messageData.type || 0
    )
    group.lastTime = formatTime(messageData.t || messageData.timestamp || new Date())

    // 如果不是当前选中的群聊，增加未读数量
    if (selectedConversation.value?.id !== group.id) {
      group.unread = (group.unread || 0) + 1
      console.log('增加未读数量:', { groupId: targetGroupId, newUnread: group.unread })
    }

    // 将有新消息的群聊移到列表顶部（只有当不在第一位时才移动）
    if (groupIndex > 0) {
      const updatedGroup = groupConversations.value.splice(groupIndex, 1)[0]
      groupConversations.value.unshift(updatedGroup)
      console.log('群聊已移到列表顶部:', targetGroupId)
    }

    // 更新聊天列表管理器中的数据
    try {
      const { updateChatListItem } = await import('@/utils/chatListManager.js')
      await updateChatListItem(targetGroupId, {
        lastMessage: group.lastMessage,
        lastTime: new Date().toISOString(),
        unreadCount: group.unread
      })
    } catch (error) {
      console.warn('更新聊天列表管理器失败:', error)
    }

    console.log('群聊最后消息更新完成:', {
      groupId: targetGroupId,
      groupName: group.name,
      oldLastMessage,
      newLastMessage: group.lastMessage,
      oldLastTime,
      newLastTime: group.lastTime,
      unread: group.unread
    })
  } else {
    console.warn('未找到对应的群聊:', { targetGroupId, availableGroups: groupConversations.value.map(g => ({ id: g.originalData?.id || g.originalData?.ID, name: g.name })) })
  }
}

// 更新好友列表中的最后一条消息
const updateFriendLastMessage = async (userId, messageData) => {
  console.log('开始更新好友最后消息:', { userId, messageData })

  // 确保userId是字符串或数字类型
  const targetUserId = String(userId)

  const friendIndex = friendConversations.value.findIndex(
    friend => String(friend.originalData?.id || friend.originalData?.ID) === targetUserId
  )

  console.log('查找好友索引:', { targetUserId, friendIndex, totalFriends: friendConversations.value.length })

  if (friendIndex !== -1) {
    const friend = friendConversations.value[friendIndex]
    const oldLastMessage = friend.lastMessage
    const oldLastTime = friend.lastTime

    // 更新最后消息内容和时间
    friend.lastMessage = formatMessageContent(
      messageData.msg || messageData.content,
      messageData.typecode2 || messageData.type || 0
    )
    friend.lastTime = formatTime(messageData.t || messageData.timestamp || new Date())

    // 如果不是当前选中的好友会话，增加未读数量
    if (selectedConversation.value?.id !== friend.id) {
      friend.unread = (friend.unread || 0) + 1
      console.log('增加好友未读数量:', { userId: targetUserId, newUnread: friend.unread })
    }

    // 将有新消息的好友会话移到列表顶部（只有当不在第一位时才移动）
    if (friendIndex > 0) {
      const updatedFriend = friendConversations.value.splice(friendIndex, 1)[0]
      friendConversations.value.unshift(updatedFriend)
      console.log('好友会话已移到列表顶部:', targetUserId)
    }

    // 更新聊天列表管理器中的数据
    try {
      const { updateChatListItem, generatePrivateChatId } = await import('@/utils/chatListManager.js')
      const currentUserId = getCurrentUserId()
      if (currentUserId) {
        const { generatePrivateChatId } = await import('@/utils/db.js')
        const privateChatId = generatePrivateChatId(currentUserId, targetUserId)
        await updateChatListItem(privateChatId, {
          lastMessage: friend.lastMessage,
          lastTime: new Date().toISOString(),
          unreadCount: friend.unread
        })
      }
    } catch (error) {
      console.warn('更新聊天列表管理器失败:', error)
    }

    console.log('好友最后消息更新完成:', {
      userId: targetUserId,
      friendName: friend.name,
      oldLastMessage,
      newLastMessage: friend.lastMessage,
      oldLastTime,
      newLastTime: friend.lastTime,
      unread: friend.unread
    })
  } else {
    console.warn('未找到对应的好友会话:', { targetUserId, availableFriends: friendConversations.value.map(f => ({ id: f.originalData?.id || f.originalData?.ID, name: f.name })) })
  }
}

// 监听自定义群聊消息更新事件
const handleGroupMessageUpdate = (event) => {
  try {
    const { groupId, messageData } = event.detail || {}
    console.log('收到群聊消息更新事件:', { groupId, messageData, eventDetail: event.detail })

    if (!groupId || !messageData) {
      console.warn('群聊消息更新事件数据不完整:', event.detail)
      return
    }

    updateGroupLastMessage(groupId, messageData)
  } catch (error) {
    console.error('处理群聊消息更新事件失败:', error, event)
  }
}

// 监听自定义好友消息更新事件
const handlePrivateMessageUpdate = (event) => {
  try {
    const { userId, messageData } = event.detail || {}
    console.log('收到好友消息更新事件:', { userId, messageData, eventDetail: event.detail })

    if (!userId || !messageData) {
      console.warn('好友消息更新事件数据不完整:', event.detail)
      return
    }

    updateFriendLastMessage(userId, messageData)
  } catch (error) {
    console.error('处理好友消息更新事件失败:', error, event)
  }
}

// 处理实时消息更新事件（用于聊天列表实时更新）
const handleNewMessageReceived = async (event) => {
  try {
    const { type, chatId, message } = event.detail || {}
    console.log('收到实时消息更新事件，更新聊天列表:', { type, chatId, message })

    if (!type || !chatId || !message) {
      console.warn('实时消息更新事件数据不完整:', event.detail)
      return
    }

    // 重新从数据库加载聊天列表以确保数据同步
    await loadChatListFromDB()

    console.log('聊天列表已实时更新')
  } catch (error) {
    console.error('处理实时消息更新事件失败:', error, event)
  }
}

// 在组件挂载时添加事件监听器
onMounted(() => {
  window.addEventListener('groupMessageUpdate', handleGroupMessageUpdate)
  window.addEventListener('privateMessageUpdate', handlePrivateMessageUpdate)
  window.addEventListener('newMessageReceived', handleNewMessageReceived)

  // 添加私聊事件监听器
  emitter.on('startPrivateChat', handleStartPrivateChat)

  console.log('已添加群聊、好友消息更新和实时消息事件监听器')
})

// 在组件卸载时移除事件监听器
onUnmounted(() => {
  window.removeEventListener('groupMessageUpdate', handleGroupMessageUpdate)
  window.removeEventListener('privateMessageUpdate', handlePrivateMessageUpdate)
  window.removeEventListener('newMessageReceived', handleNewMessageReceived)

  // 移除私聊事件监听器
  emitter.off('startPrivateChat', handleStartPrivateChat)

  console.log('已移除群聊、好友消息更新和实时消息事件监听器')
})

// 监听WebSocket消息状态变化，仅用于UI更新
watch(() => webSocketStore.lastMessage, async (newMessage) => {
  if (newMessage) {
    console.log('收到WebSocket消息，更新UI:', newMessage)

    try {
      // 注意：消息的具体处理（解密、存储等）已在 connectionService.js 中统一处理
      // 这里只负责UI相关的更新，避免重复处理消息

      // 重新从数据库加载聊天列表以确保数据同步
      await loadChatListFromDB()

      console.log('聊天列表UI已更新')
    } catch (error) {
      console.error('更新聊天列表UI失败:', error)
    }
  }
}, { deep: true })

// 注意：原来的 saveReceivedMessageToDB 函数已移除
// 消息的具体处理（解密、存储等）现在统一在 connectionService.js 中进行
// 这里只保留UI更新相关的逻辑

// 监听标签页切换
watch(activeTab, async (newTab) => {
  console.log('标签页切换到:', newTab)

  // 切换标签页时重新从数据库查询数据
  await loadChatListFromDB()

  if (newTab === 'groups') {
    // 切换到群聊时，自动选择第一个群聊
    if (groupConversations.value.length > 0) {
      selectedConversation.value = groupConversations.value[0]
    } else {
      selectedConversation.value = null
    }
  } else if (newTab === 'friends') {
    // 切换到好友时，自动选择第一个好友会话（如果有的话）
    if (friendConversations.value.length > 0) {
      selectedConversation.value = friendConversations.value[0]
    } else {
      selectedConversation.value = null
    }
  }
}, { immediate: true })

defineExpose({
  open: async (user) => {
    console.log('打开聊天对话框，用户:', user)
    currentUser.value = user
    activeTab.value = 'groups' // 默认显示群聊
    visible.value = true

    // 确保获取了聊天用户信息
    if (!userStore.formChatId) {
      console.log('获取聊天用户信息...')
      await userStore.GetChatUserInfo()
    }

    // 每次打开弹窗都重新加载数据，确保显示最新的聊天记录
    console.log('重新加载聊天数据...')
    await loadChatListFromDB() // 先加载数据库中的聊天列表
    await fetchGroupList() // 再获取API数据

    // 如果有群组数据，自动选择第一个
    if (groupConversations.value.length > 0) {
      selectedConversation.value = groupConversations.value[0]
      console.log('自动选择群组:', selectedConversation.value)
    }

    console.log('聊天对话框打开完成，群聊数量:', groupConversations.value.length, '私聊数量:', friendConversations.value.length)
  },
  close: closeDialog
})
</script>
<style>
.el-dialog.chat-dialog{
  height: 680px !important;
}
.el-dialog{
  border-radius: 20px;
}
.el-dialog__header{
  padding-bottom: 0 !important;
  padding: 0 !important;
}
.el-dialog__body{
  /* height: calc(100% - 60px) !important; */
  height: 100%;
  background: #1f2937;
}
.el-dialog.chat-dialog{
  height: 680px;
  padding: 0;
}
.el-empty {
  --el-empty-description-color: #9ca3af;
}
.el-empty__description p {
  color: #9ca3af !important;
}
</style>
<style lang="scss" scoped>
// 样式已移至 styles/chat.scss 文件中

.dialog-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 12px 16px;
  background: #1f2937;
  color: white;
  border-bottom: 1px solid #374151;
  border-radius: 10px 10px 0 0;

  .header-left {
    display: flex;
    align-items: center;
    gap: 10px;

    .username {
      font-weight: 500;
      font-size: 16px;
    }
  }

  .header-right {
    display: flex;
    align-items: center;
    gap: 15px;

    .chat-count {
      font-size: 14px;
      opacity: 0.8;
    }
  }
}

/* All layout styles moved to Tailwind CSS classes */
</style>
