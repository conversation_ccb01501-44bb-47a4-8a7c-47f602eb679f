import { defineStore } from 'pinia'
import { ref, computed } from 'vue'
import webSocketManager from '@/utils/websocket'
import { getChatList, getChatMessages, markMessagesAsRead } from '@/utils/db.js'

export const useWebSocketStore = defineStore('websocket', () => {
  // 状态
  const connectionStatus = ref('disconnected') // disconnected, connecting, connected
  const lastMessage = ref(null)
  const messageHistory = ref([])
  const unreadCount = ref(0)
  const autoReconnectEnabled = ref(true) // 自动重连开关

  // 计算属性
  const isConnected = computed(() => connectionStatus.value === 'connected')
  const isConnecting = computed(() => connectionStatus.value === 'connecting')

  // 初始化WebSocket连接
  const initConnection = async (token, persistent = true) => {
    try {
      connectionStatus.value = 'connecting'
      await webSocketManager.connect(token, persistent)
      connectionStatus.value = 'connected'

      // 设置消息处理器
      setupMessageHandlers()

      console.log('WebSocket连接已建立')
      return true
    } catch (error) {
      connectionStatus.value = 'disconnected'
      console.error('WebSocket连接失败:', error)

      // 持久连接模式下，连接失败也不抛出错误，让内部重连机制处理
      if (persistent && autoReconnectEnabled.value && token) {
        console.log('持久连接模式：连接失败，但重连机制将自动处理')
        return false // 返回false表示初始连接失败，但不阻止后续流程
      }

      throw error
    }
  }

  // 设置消息处理器
  const setupMessageHandlers = () => {
    // 处理所有消息 - 只负责更新 Pinia 状态，不处理具体业务逻辑
    webSocketManager.addMessageHandler('all', (data) => {
      // 所有WebSocket消息都设置到 lastMessage 和 messageHistory（包括自己发送的回显）
      lastMessage.value = data
      messageHistory.value.push({
        ...data,
        timestamp: new Date().toISOString()
      })

      // 限制消息历史记录数量
      if (messageHistory.value.length > 100) {
        messageHistory.value = messageHistory.value.slice(-100)
      }

      // 注意：不再在这里处理具体的消息业务逻辑，避免重复处理
      // 具体的消息处理统一在 connectionService.js 中进行
    })
  }

  // 启用持久连接
  const enablePersistentConnection = () => {
    webSocketManager.enablePersistentConnection()
  }

  // 禁用持久连接
  const disablePersistentConnection = () => {
    webSocketManager.disablePersistentConnection()
  }

  // 发送消息
  const sendMessage = (data) => {
    return webSocketManager.send(data)
  }

  // 关闭连接
  const closeConnection = () => {
    connectionStatus.value = 'disconnected'
    webSocketManager.close()
  }

  // 强制重连
  const forceReconnect = async (token) => {
    try {
      connectionStatus.value = 'connecting'
      await webSocketManager.forceReconnect(token)
      connectionStatus.value = 'connected'
      return true
    } catch (error) {
      connectionStatus.value = 'disconnected'
      throw error
    }
  }

  // 获取连接状态
  const getConnectionStatus = () => {
    return webSocketManager.getStatus()
  }

  // 检查是否已连接
  const checkIsConnected = () => {
    return webSocketManager.isConnected()
  }

  // 从数据库加载聊天列表
  const loadChatList = async () => {
    try {
      const chatList = await getChatList()
      return chatList
    } catch (error) {
      console.error('加载聊天列表失败:', error)
      return []
    }
  }

  // 从数据库加载聊天消息
  const loadChatMessages = async (chatId, chatType = 'private', page = 1, pageSize = 20) => {
    try {
      const messages = await getChatMessages(chatId, chatType, page, pageSize)
      return messages
    } catch (error) {
      console.error('加载聊天消息失败:', error)
      return []
    }
  }

  // 标记消息为已读
  const markChatAsRead = async (chatId, chatType = 'private') => {
    try {
      await markMessagesAsRead(chatId, chatType)
      console.log('消息已标记为已读:', chatId)
    } catch (error) {
      console.error('标记消息已读失败:', error)
    }
  }

  // 清除未读计数
  const clearUnreadCount = () => {
    unreadCount.value = 0
    console.log('未读计数已清除')
  }

  // 清除消息历史
  const clearMessageHistory = () => {
    messageHistory.value = []
    lastMessage.value = null
    console.log('消息历史已清除')
  }

  // 重新连接
  const reconnect = async (token, persistent = true) => {
    if (!token) {
      console.error('重连失败：token不能为空')
      throw new Error('token不能为空')
    }

    // 启用自动重连
    autoReconnectEnabled.value = true

    if (connectionStatus.value === 'connecting') {
      console.log('正在连接中，请稍候...')
      return
    }

    try {
      await initConnection(token, persistent)
    } catch (error) {
      console.error('重连失败:', error)
      throw error
    }
  }

  return {
    // 状态
    connectionStatus,
    lastMessage,
    messageHistory,
    unreadCount,
    autoReconnectEnabled,

    // 计算属性
    isConnected,
    isConnecting,

    // 方法
    initConnection,
    enablePersistentConnection,
    disablePersistentConnection,
    sendMessage,
    closeConnection,
    forceReconnect,
    getConnectionStatus,
    checkIsConnected,
    loadChatList,
    loadChatMessages,
    markChatAsRead,
    clearUnreadCount,
    clearMessageHistory,
    reconnect
  }
})